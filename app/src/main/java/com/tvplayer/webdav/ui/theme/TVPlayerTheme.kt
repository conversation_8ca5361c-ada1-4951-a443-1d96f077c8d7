package com.tvplayer.webdav.ui.theme

import androidx.compose.runtime.Composable
import androidx.tv.material3.MaterialTheme
import androidx.tv.material3.darkColorScheme
import androidx.tv.material3.lightColorScheme
import androidx.compose.ui.graphics.Color

/**
 * TV Player 应用的 Compose 主题
 * 基于 TV Material 3 设计系统
 */

// 定义应用的颜色方案
private val DarkColorScheme = darkColorScheme(
    primary = Color(0xFF1976D2),           // 主色调 - 蓝色
    onPrimary = Color.White,
    primaryContainer = Color(0xFF0D47A1),  // 主色容器
    onPrimaryContainer = Color.White,
    
    secondary = Color(0xFF03DAC6),          // 次要色调 - 青色
    onSecondary = Color.Black,
    secondaryContainer = Color(0xFF018786),
    onSecondaryContainer = Color.White,
    
    tertiary = Color(0xFFFF5722),           // 第三色调 - 橙色（用于强调）
    onTertiary = Color.White,
    
    background = Color(0xFF121212),         // 深色背景
    onBackground = Color.White,
    
    surface = Color(0xFF1E1E1E),           // 表面色
    onSurface = Color.White,
    surfaceVariant = Color(0xFF2C2C2C),
    onSurfaceVariant = Color(0xFFE0E0E0),
    
    error = Color(0xFFCF6679),
    onError = Color.Black,
    
    outline = Color(0xFF5F5F5F),
    outlineVariant = Color(0xFF404040)
)

private val LightColorScheme = lightColorScheme(
    primary = Color(0xFF1976D2),
    onPrimary = Color.White,
    primaryContainer = Color(0xFFBBDEFB),
    onPrimaryContainer = Color(0xFF0D47A1),
    
    secondary = Color(0xFF03DAC6),
    onSecondary = Color.Black,
    secondaryContainer = Color(0xFFB2DFDB),
    onSecondaryContainer = Color(0xFF00695C),
    
    tertiary = Color(0xFFFF5722),
    onTertiary = Color.White,
    
    background = Color(0xFFFFFBFE),
    onBackground = Color(0xFF1C1B1F),
    
    surface = Color(0xFFFFFBFE),
    onSurface = Color(0xFF1C1B1F),
    surfaceVariant = Color(0xFFE7E0EC),
    onSurfaceVariant = Color(0xFF49454F),
    
    error = Color(0xFFB3261E),
    onError = Color.White,
    
    outline = Color(0xFF79747E),
    outlineVariant = Color(0xFFCAC4D0)
)

@Composable
fun TVPlayerTheme(
    darkTheme: Boolean = true, // TV 应用通常使用深色主题
    content: @Composable () -> Unit
) {
    val colorScheme = if (darkTheme) {
        DarkColorScheme
    } else {
        LightColorScheme
    }

    MaterialTheme(
        colorScheme = colorScheme,
        typography = TVPlayerTypography,
        content = content
    )
}
