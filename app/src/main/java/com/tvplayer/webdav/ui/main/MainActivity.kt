package com.tvplayer.webdav.ui.main

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.ui.Modifier
import androidx.tv.material3.Surface
import com.tvplayer.webdav.ui.home.HomeScreen
import com.tvplayer.webdav.ui.theme.TVPlayerTheme
import dagger.hilt.android.AndroidEntryPoint

/**
 * Main Activity for Android TV
 * 使用 Compose 构建的现代化 TV 界面
 */
@AndroidEntryPoint
class MainActivity : ComponentActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        setContent {
            TVPlayerTheme {
                Surface(
                    modifier = Modifier.fillMaxSize()
                ) {
                    HomeScreen()
                }
            }
        }
    }
}
