package com.tvplayer.webdav.ui.home

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.tv.material3.*
import com.tvplayer.webdav.ui.home.components.MediaCard
import com.tvplayer.webdav.ui.home.components.SectionHeader
import com.tvplayer.webdav.ui.utils.autoFocus
import com.tvplayer.webdav.ui.utils.tvFocusable

/**
 * TV Player 首页 Compose 界面 - 主入口
 */
@Composable
fun HomeScreen() {
    HomeScreenAdapter()
}

/**
 * TV Player 首页内容组件
 * 采用现代化的 TV Material 3 设计
 */
@Composable
fun HomeScreenContent(
    uiState: HomeUiState,
    onRetry: () -> Unit,
    onMediaClick: (MediaItem) -> Unit,
    onSettingsClick: () -> Unit
) {
    val focusRequester = remember { FocusRequester() }

    LaunchedEffect(Unit) {
        // 设置初始焦点
        focusRequester.requestFocus()
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(horizontal = 48.dp, vertical = 32.dp)
    ) {
        // 应用标题
        Text(
            text = "AJ TV Player",
            style = MaterialTheme.typography.displayMedium,
            color = MaterialTheme.colorScheme.primary,
            modifier = Modifier.padding(bottom = 32.dp)
        )

        when {
            uiState.isLoading -> {
                LoadingContent()
            }
            uiState.hasError -> {
                ErrorContent(
                    message = uiState.errorMessage ?: "加载失败",
                    onRetry = onRetry
                )
            }
            else -> {
                HomeContent(
                    uiState = uiState,
                    focusRequester = focusRequester,
                    onMediaClick = onMediaClick,
                    onSettingsClick = onSettingsClick
                )
            }
        }
    }
}

@Composable
private fun HomeContent(
    uiState: HomeUiState,
    focusRequester: FocusRequester,
    onMediaClick: (MediaItem) -> Unit,
    onSettingsClick: () -> Unit
) {
    LazyColumn(
        verticalArrangement = Arrangement.spacedBy(24.dp)
    ) {
        // 快速操作区域
        item {
            QuickActionsSection(
                focusRequester = focusRequester,
                onSettingsClick = onSettingsClick
            )
        }

        // 播放历史
        if (uiState.playbackHistory.isNotEmpty()) {
            item {
                SectionHeader(title = "最近播放")
            }
            item {
                MediaRow(
                    items = uiState.playbackHistory,
                    onItemClick = onMediaClick
                )
            }
        }

        // 电影
        if (uiState.movies.isNotEmpty()) {
            item {
                SectionHeader(title = "电影")
            }
            item {
                MediaRow(
                    items = uiState.movies,
                    onItemClick = onMediaClick
                )
            }
        }

        // 电视剧
        if (uiState.tvShows.isNotEmpty()) {
            item {
                SectionHeader(title = "电视剧")
            }
            item {
                MediaRow(
                    items = uiState.tvShows,
                    onItemClick = onMediaClick
                )
            }
        }
    }
}

@Composable
private fun QuickActionsSection(
    focusRequester: FocusRequester,
    onSettingsClick: () -> Unit
) {
    Row(
        horizontalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Button(
            onClick = {
                // TODO: 连接 WebDAV 服务器
            },
            modifier = Modifier
                .focusRequester(focusRequester)
                .autoFocus()
        ) {
            Text("连接服务器")
        }

        Button(
            onClick = {
                // TODO: 浏览文件
            }
        ) {
            Text("浏览文件")
        }

        Button(
            onClick = onSettingsClick
        ) {
            Text("设置")
        }
    }
}

@Composable
private fun MediaRow(
    items: List<MediaItem>,
    onItemClick: (MediaItem) -> Unit
) {
    LazyRow(
        horizontalArrangement = Arrangement.spacedBy(16.dp),
        contentPadding = PaddingValues(horizontal = 8.dp)
    ) {
        items(items) { mediaItem ->
            MediaCard(
                mediaItem = mediaItem,
                onClick = { onItemClick(mediaItem) }
            )
        }
    }
}

@Composable
private fun LoadingContent() {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // TODO: 添加加载动画
            Text(
                text = "加载中...",
                style = MaterialTheme.typography.headlineMedium,
                textAlign = TextAlign.Center
            )
        }
    }
}

@Composable
private fun ErrorContent(
    message: String,
    onRetry: () -> Unit
) {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            Text(
                text = message,
                style = MaterialTheme.typography.headlineMedium,
                color = MaterialTheme.colorScheme.error,
                textAlign = TextAlign.Center
            )
            Button(onClick = onRetry) {
                Text("重试")
            }
        }
    }
}

// 数据类定义
data class HomeUiState(
    val isLoading: Boolean = false,
    val hasError: Boolean = false,
    val errorMessage: String? = null,
    val playbackHistory: List<MediaItem> = emptyList(),
    val movies: List<MediaItem> = emptyList(),
    val tvShows: List<MediaItem> = emptyList()
)

data class MediaItem(
    val id: String,
    val title: String,
    val description: String? = null,
    val thumbnailUrl: String? = null,
    val duration: Long? = null,
    val type: MediaType
)

enum class MediaType {
    MOVIE, TV_SHOW, VIDEO
}
