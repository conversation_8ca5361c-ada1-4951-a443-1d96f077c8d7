package com.tvplayer.webdav.ui.home

import androidx.compose.foundation.layout.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.unit.dp
import androidx.tv.material3.*
import kotlinx.coroutines.delay

/**
 * TV Player 首页 Compose 界面 - 主入口
 */
@Composable
fun HomeScreen() {
    // 暂时使用简化版本确保编译成功
    SimpleHomeScreen()
}

/**
 * 简化的首页界面，确保编译成功
 */
@Composable
fun SimpleHomeScreen() {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(48.dp),
        verticalArrangement = Arrangement.Center,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = "AJ TV Player",
            style = MaterialTheme.typography.displayMedium,
            color = MaterialTheme.colorScheme.primary
        )

        Spacer(modifier = Modifier.height(32.dp))

        Text(
            text = "现代化 TV Compose 界面",
            style = MaterialTheme.typography.headlineMedium,
            color = MaterialTheme.colorScheme.onBackground
        )

        Spacer(modifier = Modifier.height(48.dp))

        Button(
            onClick = { /* TODO: 实现功能 */ },
            modifier = Modifier.autoFocus()
        ) {
            Text("开始使用")
        }
    }
}

/**
 * 简化的自动焦点修饰符
 */
@Composable
fun Modifier.autoFocus(): Modifier {
    val focusRequester = remember { FocusRequester() }

    LaunchedEffect(Unit) {
        delay(100)
        try {
            focusRequester.requestFocus()
        } catch (e: Exception) {
            // 忽略焦点请求失败
        }
    }

    return this.focusRequester(focusRequester)
}
