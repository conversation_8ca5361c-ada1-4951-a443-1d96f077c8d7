package com.tvplayer.webdav.ui.home

import androidx.compose.runtime.*
import androidx.compose.runtime.livedata.observeAsState
import androidx.hilt.navigation.compose.hiltViewModel
import com.tvplayer.webdav.data.model.MediaItem
import com.tvplayer.webdav.data.model.MediaType
import com.tvplayer.webdav.data.model.TVSeriesSummary

/**
 * HomeScreen 的适配器，用于桥接现有的 HomeViewModel 和新的 Compose UI
 */
@Composable
fun HomeScreenAdapter(
    viewModel: HomeViewModel = hiltViewModel()
) {
    // 观察现有 ViewModel 的 LiveData
    val playbackHistory by viewModel.playbackHistory.observeAsState(emptyList())
    val movies by viewModel.movies.observeAsState(emptyList())
    val tvShows by viewModel.tvShows.observeAsState(emptyList())
    val isLoading by viewModel.isLoading.observeAsState(false)
    val error by viewModel.error.observeAsState()

    // 转换为 Compose 状态
    val uiState = remember(playbackHistory, movies, tvShows, isLoading, error) {
        HomeUiState(
            isLoading = isLoading,
            hasError = error != null,
            errorMessage = error,
            playbackHistory = playbackHistory.map { it.toComposeMediaItem() },
            movies = movies.map { it.toComposeMediaItem() },
            tvShows = tvShows.map { it.toComposeMediaItem() }
        )
    }

    // 初始化数据加载
    LaunchedEffect(Unit) {
        viewModel.loadHomeData()
    }

    // 使用新的 Compose UI
    HomeScreenContent(
        uiState = uiState,
        onRetry = { viewModel.loadHomeData() },
        onMediaClick = { mediaItem ->
            // TODO: 导航到播放器
        },
        onSettingsClick = {
            // TODO: 导航到设置
        }
    )
}

/**
 * 将现有的 MediaItem 转换为 Compose 版本
 */
private fun MediaItem.toComposeMediaItem(): com.tvplayer.webdav.ui.home.MediaItem {
    return com.tvplayer.webdav.ui.home.MediaItem(
        id = this.id,
        title = this.title,
        description = this.description,
        thumbnailUrl = this.thumbnailUrl,
        duration = this.duration,
        type = when (this.type) {
            MediaType.MOVIE -> com.tvplayer.webdav.ui.home.MediaType.MOVIE
            MediaType.TV_SERIES -> com.tvplayer.webdav.ui.home.MediaType.TV_SHOW
            else -> com.tvplayer.webdav.ui.home.MediaType.VIDEO
        }
    )
}

/**
 * 将 TVSeriesSummary 转换为 Compose MediaItem
 */
private fun TVSeriesSummary.toComposeMediaItem(): com.tvplayer.webdav.ui.home.MediaItem {
    return com.tvplayer.webdav.ui.home.MediaItem(
        id = this.id,
        title = this.title,
        description = "${this.seasonCount} 季 · ${this.episodeCount} 集",
        thumbnailUrl = this.posterUrl,
        duration = null,
        type = com.tvplayer.webdav.ui.home.MediaType.TV_SHOW
    )
}
