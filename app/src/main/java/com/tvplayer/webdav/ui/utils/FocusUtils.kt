package com.tvplayer.webdav.ui.utils

import androidx.compose.foundation.focusable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.platform.LocalFocusManager
import kotlinx.coroutines.delay

/**
 * TV 焦点管理工具
 * 提供 TV 应用常用的焦点管理功能
 */

/**
 * 自动焦点修饰符
 * 在组件首次显示时自动获取焦点
 */
@Composable
fun Modifier.autoFocus(
    delayMs: Long = 100
): Modifier {
    val focusRequester = remember { FocusRequester() }
    
    LaunchedEffect(Unit) {
        delay(delayMs)
        try {
            focusRequester.requestFocus()
        } catch (e: Exception) {
            // 忽略焦点请求失败
        }
    }
    
    return this.focusRequester(focusRequester)
}

/**
 * 焦点状态跟踪修饰符
 */
@Composable
fun Modifier.trackFocus(
    onFocusChanged: (Boolean) -> Unit
): Modifier {
    return this.onFocusChanged { focusState ->
        onFocusChanged(focusState.isFocused)
    }
}

/**
 * TV 安全焦点修饰符
 * 确保组件在 TV 环境下可以正确获取和失去焦点
 */
@Composable
fun Modifier.tvFocusable(
    enabled: Boolean = true
): Modifier {
    return if (enabled) {
        this.focusable()
    } else {
        this
    }
}

/**
 * 焦点恢复容器
 * 用于在页面返回时恢复之前的焦点状态
 */
@Composable
fun FocusRestoreContainer(
    key: String,
    modifier: Modifier = Modifier,
    content: @Composable () -> Unit
) {
    val focusManager = LocalFocusManager.current
    var lastFocusedIndex by remember(key) { mutableStateOf(0) }
    
    Box(
        modifier = modifier.fillMaxSize()
    ) {
        content()
    }
    
    // 在组件销毁时保存焦点状态
    DisposableEffect(key) {
        onDispose {
            // 这里可以保存焦点状态到持久化存储
        }
    }
}

/**
 * TV 导航辅助工具
 */
object TVNavigationHelper {
    
    /**
     * 检查是否为 TV 设备
     */
    fun isTVDevice(): Boolean {
        // 这里可以添加更复杂的 TV 设备检测逻辑
        return true // 暂时返回 true，因为这是 TV 应用
    }
    
    /**
     * 获取推荐的焦点延迟时间
     */
    fun getRecommendedFocusDelay(): Long {
        return if (isTVDevice()) 200L else 100L
    }
}
